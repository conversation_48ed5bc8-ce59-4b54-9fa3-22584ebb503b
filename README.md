# Mem0 记忆系统

基于 mem0ai 构建的记忆系统，支持使用嵌入模型、向量数据库和图数据库进行高效的知识存储和检索。

## 功能特点

- 使用 OpenAI 兼容 API 的嵌入模型 (BAAI/bge-large-zh-v1.5)
- 通过 OpenAI 兼容中转 API 使用 Gemini 大语言模型
- Qdrant 向量数据库用于高效相似度搜索
- Neo4j 图数据库用于复杂关系存储
- 自动处理 Gemini 格式响应的适配器

## 设置指南

### 1. 环境准备

确保安装了 Python 3.9+ 和以下服务:

- **Qdrant** (向量数据库)：[安装指南](https://qdrant.tech/documentation/guides/installation/)
- **Neo4j** (图数据库)：[安装指南](https://neo4j.com/docs/operations-manual/current/installation/)

### 2. 安装依赖

创建并激活虚拟环境：

```bash
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境（Windows）
.venv\Scripts\activate

# 激活虚拟环境（Linux/Mac）
source .venv/bin/activate
```

安装必要的包：

```bash
pip install mem0ai neo4j python-dotenv langchain-neo4j rank-bm25
```

### 3. 配置环境变量

复制 `env_example.txt` 为 `.env` 并填入实际值：

```bash
cp env_example.txt .env
```

编辑 `.env` 文件，配置以下内容：

```
# 嵌入模型API (siliconflow.cn)
EMBEDDINGS_API_KEY=你的_API_密钥
EMBEDDINGS_API_URL=https://api.siliconflow.cn/v1

# OpenAI兼容API (中转到Gemini)
LLM_API_KEY=你的_API_密钥
LLM_API_URL=https://gemini-cli-worker-staging.13467879663.workers.dev/v1
LLM_MODEL=gemini-2.5-pro

# Neo4j数据库设置
NEO4J_URI=neo4j://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=你的密码

# Qdrant向量数据库设置
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_COLLECTION=mem0_memories
```

### 4. 启动服务

#### 启动 Qdrant

Windows:
```bash
# 使用Docker
docker run -p 6333:6333 -p 6334:6334 qdrant/qdrant

# 或使用二进制版本
.\qdrant.exe
```

Linux/Mac:
```bash
# 使用Docker
docker run -p 6333:6333 -p 6334:6334 qdrant/qdrant

# 或使用二进制版本
./qdrant
```

#### 启动 Neo4j

Windows:
```bash
neo4j.bat console
```

Linux/Mac:
```bash
neo4j console
```

### 5. 运行示例

```bash
python example.py
```

## API 使用说明

### 添加记忆

```python
from app import add_memory

# 添加单条记忆
result = add_memory("这是一条测试记忆", user_id="用户ID")

# 添加对话记忆
from mem0 import Memory
from mem0_config import config

memory = Memory.from_config(config)
conversation = [
    {"role": "user", "content": "我喜欢喝绿茶"},
    {"role": "assistant", "content": "明白了，您喜欢喝绿茶"},
    {"role": "user", "content": "是的，特别是龙井茶"}
]
memory.add(conversation, user_id="用户ID")
```

### 搜索记忆

```python
from app import search_memory

# 搜索相关记忆
results = search_memory("搜索关键词", user_id="用户ID", limit=5)
```

### 获取所有记忆

```python
from app import get_all_memories

# 获取用户所有记忆
all_memories = get_all_memories(user_id="用户ID")
```

## 项目结构

- `mem0_config.py`: 配置文件，包含所有组件的设置
- `app.py`: 主应用程序，提供记忆操作的API
- `example.py`: 示例脚本，展示如何使用记忆系统
- `openai_adapter.py`: 适配器，处理Gemini格式的响应
- `env_example.txt`: 环境变量示例文件

## 故障排除

### API连接问题

如果遇到API身份验证错误：
1. 检查API密钥是否正确
2. 确认API端点是否可用
3. 检查网络连接

### 数据库连接问题

如果无法连接到Qdrant或Neo4j：
1. 确认服务是否正在运行
2. 检查端口配置是否正确
3. 确认防火墙设置是否允许连接

### 向量维度不匹配

如果遇到向量维度错误，程序会自动删除并重新创建具有正确维度的集合。如果仍然有问题：
1. 手动删除Qdrant集合: `http://localhost:6333/collections/mem0_memories`
2. 重新运行程序