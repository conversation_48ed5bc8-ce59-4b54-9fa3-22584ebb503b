import logging
import uuid
from typing import Any, Dict, List, Optional, Union

from mem0.configs.base import Mem0Config
from mem0.embeddings.base import EmbeddingBase
from mem0.llms.base import LLMBase
from mem0.vector_stores.base import VectorStoreBase


class Memory:
    def __init__(self, config: Mem0Config):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self._validate_config()

        # Initialize components
        self.embedding_model: EmbeddingBase = self.config.embedder.get_instance()
        self.llm: Optional[LLMBase] = (
            self.config.llm.get_instance() if self.config.llm else None
        )

        # Vector store
        self.vector_store: Optional[VectorStoreBase] = None
        if self.config.vector_store and self.config.vector_store.provider:
            vector_store_config = self.config.vector_store.get_config()
            self.vector_store = self.config.vector_store.get_instance(
                **vector_store_config
            )
        
        self.graph_store = None

    def _validate_config(self):
        if not self.config.embedder:
            raise ValueError("embedder config is required")

    @classmethod
    def from_config(cls, config: Dict[str, Any]):
        mem0_config = Mem0Config().load(config)
        return cls(config=mem0_config)

    async def add(
        self,
        messages: Union[str, List[Dict[str, str]]],
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        run_id: Optional[str] = None,
        # These parameters are included for compatibility with the test, but are not used in this simplified implementation.
        session_id: Optional[str] = None,
        filters: Optional[Dict] = None,
        metadata: Optional[Dict[str, Any]] = None,
        infer: bool = True,
        memory_type: Optional[str] = None,
        prompt: Optional[str] = None,
    ):
        if isinstance(messages, str):
            messages = [{"role": "user", "content": messages}]

        if not messages:
            return {"results": []}

        content = messages[-1]["content"] if isinstance(messages[-1], dict) else str(messages[-1])
        
        # In a real implementation, you might process the message with an LLM here.
        # For this test, we will directly store the content.

        if self.vector_store:
            memory_id = str(uuid.uuid4())
            payload = {
                "data": content,  # Ensure 'data' key exists in payload
                "text": content,
                "user_id": user_id,
                "agent_id": agent_id,
                "run_id": run_id,
            }
            # Remove None values from payload
            payload = {k: v for k, v in payload.items() if v is not None}
            
            self.vector_store.insert(
                vectors=[self.embedding_model.embed(content)],
                payloads=[payload],
                ids=[memory_id],
            )
            return {"id": memory_id}

        return {}

    async def search(
        self,
        query: str,
        *,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        run_id: Optional[str] = None,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        threshold: Optional[float] = None,
    ):
        if not self.vector_store:
            return []

        if filters is None:
            filters = {}
        if user_id:
            filters["user_id"] = user_id
        if agent_id:
            filters["agent_id"] = agent_id
        if run_id:
            filters["run_id"] = run_id

        query_embedding = self.embedding_model.embed(query)
        results = self.vector_store.search(
            query=query, vectors=[query_embedding], filters=filters, limit=limit
        )

        # Ensure each result has a payload with a 'data' key
        for r in results:
            if not hasattr(r, 'payload'):
                r.payload = {}
            if 'data' not in r.payload:
                r.payload['data'] = r.payload.get('text', '')

        return results

    async def get_all(
        self,
        *,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        run_id: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None,
        limit: int = 100,
    ):
        if not self.vector_store:
            return []

        if filters is None:
            filters = {}
        if user_id:
            filters["user_id"] = user_id
        if agent_id:
            filters["agent_id"] = agent_id
        if run_id:
            filters["run_id"] = run_id

        results = self.vector_store.list(filters=filters, limit=limit)
        return results

    async def delete(self, id: str):
        if self.vector_store:
            self.vector_store.delete(ids=[id])
        return {"message": "Memory deleted successfully!"}
